// DOM Elements
const header = document.querySelector('.header');
const navToggle = document.querySelector('.nav-toggle');
const navList = document.querySelector('.nav-list');
const cursor = document.querySelector('.mouse-cursor');
const cursorBorder = document.querySelector('.mouse-cursor-border');

// Custom cursor
document.addEventListener('mousemove', (e) => {
    cursor.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
    cursorBorder.style.transform = `translate(${e.clientX - 20}px, ${e.clientY - 20}px)`;
});

// Cursor hover effect for interactive elements
document.querySelectorAll('a, button').forEach(element => {
    element.addEventListener('mouseenter', () => {
        cursor.style.transform = 'scale(1.5)';
        cursorBorder.style.transform = 'scale(1.5)';
    });
    
    element.addEventListener('mouseleave', () => {
        cursor.style.transform = 'scale(1)';
        cursorBorder.style.transform = 'scale(1)';
    });
});

// Toggle mobile navigation
navToggle.addEventListener('click', () => {
    navList.classList.toggle('active');
    
    // Animate hamburger to X
    const hamburger = navToggle.querySelector('.hamburger');
    hamburger.classList.toggle('active');
});

// Header background on scroll
window.addEventListener('scroll', () => {
    if (window.scrollY > 50) {
        header.style.background = 'rgba(10, 10, 10, 0.98)';
    } else {
        header.style.background = 'rgba(10, 10, 10, 0.95)';
    }
});

// Smooth scroll for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            // Close mobile menu if open
            navList.classList.remove('active');
        }
    });
});
