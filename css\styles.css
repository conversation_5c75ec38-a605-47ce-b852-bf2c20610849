/* Base Styles */
@import url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&display=swap');

:root {
    /* Colors */
    --bg-primary: #0f1211;
    --bg-secondary: #151a19;
    --bg-transparent: rgba(15, 18, 17, 0.97);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);

    --accent-primary: #f72323;
    --accent-light: #ff3333;
    --accent-dark: #e52020;

    --border-light: rgba(255, 255, 255, 0.05);
    --border-medium: rgba(255, 255, 255, 0.2);
    --border-hover: rgba(255, 255, 255, 0.4);
    --overlay-light: rgba(255, 255, 255, 0.1);
    
    /* Shadows */
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.2);

    --shadow-accent: 0 4px 15px rgba(247, 35, 35, 0.3);
    --shadow-accent-hover: 0 6px 20px rgba(116, 0, 0, 0.4);
    
    /* Typography */
    --font-family: "Geist Mono", monospace;
    --fw-regular: 400;
    --fw-medium: 500;
    --fw-semibold: 600;
    --fw-bold: 700;
    
    /* Spacing */
    --space-xs: 5px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 20px;
    --space-xl: 25px;
    --space-xxl: 50px;
    --space-xxxl: 100px;
    
    /* Border radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Layout */
    --container-max: 1500px;
    --header-height: 80px;
}

/* Custom Cursor */
.mouse-cursor {
    position: fixed;
    left: 0;
    top: 0;
    pointer-events: none;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    background-color: var(--color-accent);
    z-index: 9999;
    transition: transform 0.1s ease;
}

.mouse-cursor-border {
    --size: 40px;
    position: fixed;
    left: 0;
    top: 0;
    pointer-events: none;
    border-radius: 50%;
    width: var(--size);
    height: var(--size);
    border: 1px solid var(--color-accent);
    z-index: 9999;
    transition: transform 0.15s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

body {
    /* background-color: var(--bg-primary); */
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-light);
}

/* Animated Background */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    filter: blur(80px);
    opacity: 0.15;
    border-radius: 50%;
}

.shape-red {
    background: var(--accent-primary);
    width: 600px;
    height: 600px;
    top: -200px;
    right: -200px;
    animation: float1 15s ease-in-out infinite;
}

.shape-purple {
    background: var(--accent-primary);
    width: 500px;
    height: 500px;
    bottom: -200px;
    left: -150px;
    animation: float2 18s ease-in-out infinite;
}

.shape-blue {
    background: var(--accent-primary);
    width: 400px;
    height: 400px;
    top: 50%;
    left: 40%;
    animation: float3 20s ease-in-out infinite;
}

/* Grid overlay effect */
.grid-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(18, 18, 18, 0.7) 1px, transparent 1px),
        linear-gradient(90deg, rgba(18, 18, 18, 0.7) 1px, transparent 1px);
    background-size: 30px 30px;
    background-position: center center;
    z-index: -1;
    opacity: 0.2;
}

/* Connection points and lines */
.connection-point {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: var(--accent-primary);
    border-radius: 50%;
    opacity: 0.4;
    z-index: -1;
    filter: blur(1px);
    animation: pulse 3s infinite alternate;
}

.connection-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
    opacity: 0.2;
    z-index: -1;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--bg-transparent);
    backdrop-filter: blur(8px);
    transition: var(--transition-base);
    border-bottom: 1px solid var(--border-light);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-family: var(--font-family);
    font-size: 1.5rem;
    color: var(--text-primary);
    text-decoration: none;
    letter-spacing: 2px;
    font-weight: var(--fw-bold);
}

/* Navigation Styles */
.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--color-text);
    text-decoration: none;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--color-accent);
}

.nav-toggle {
    display: none;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(45deg, #0a0a0a, #1a1a1a);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../assets/perfume-bg.jpg') center/cover no-repeat;
    opacity: 0.3;
    animation: fadeIn 2s ease-out;
}

.hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    padding: 2rem;
    max-width: 800px;
    animation: slideUp 1s ease-out;
}

.accent-line {
    width: 2px;
    height: 60px;
    background: linear-gradient(to bottom, transparent, var(--color-accent), transparent);
    margin: 0 auto 2rem;
    animation: pulseHeight 2s ease-in-out infinite;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: clamp(2.5rem, 8vw, 4.5rem);
    margin-bottom: 1rem;
    line-height: 1.2;
    background: linear-gradient(120deg, #ffffff, var(--color-accent));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: titleGlow 3s ease-in-out infinite;
}

.hero-subtitle {
    font-size: clamp(1rem, 3vw, 1.25rem);
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    cursor: pointer;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-sm);
    font-weight: var(--fw-medium);
    box-shadow: var(--shadow-accent);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        120deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: 0.5s;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(220, 20, 60, 0.3);
}

.cta-button:hover::before {
    left: 100%;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

.floating-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background: var(--color-accent);
    border-radius: 50%;
    filter: blur(1px);
}

.dot1 { animation: float1 20s infinite; }
.dot2 { animation: float2 25s infinite; }
.dot3 { animation: float3 30s infinite; }

.gradient-sphere {
    position: absolute;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, var(--color-accent) 0%, transparent 70%);
    opacity: 0.1;
    filter: blur(40px);
    animation: sphereFloat 15s ease-in-out infinite;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.3;
    }
}

@keyframes float1 {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(-50px, 50px) rotate(5deg);
    }
    50% {
        transform: translate(20px, -30px) rotate(-5deg);
    }
    75% {
        transform: translate(40px, 20px) rotate(3deg);
    }
}

@keyframes float2 {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(60px, -40px) rotate(-3deg);
    }
    50% {
        transform: translate(-30px, 20px) rotate(5deg);
    }
    75% {
        transform: translate(-20px, -30px) rotate(-2deg);
    }
}

@keyframes float3 {
    0%, 100% {
        transform: translate(0, 0) scale(1) rotate(0deg);
    }
    33% {
        transform: translate(-50px, 30px) scale(1.05) rotate(-3deg);
    }
    66% {
        transform: translate(30px, -50px) scale(0.95) rotate(3deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.4;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.6;
    }
}

@keyframes float1 {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(100px, 50px); }
    50% { transform: translate(50px, 100px); }
    75% { transform: translate(-50px, 50px); }
}

@keyframes float2 {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(-70px, 120px); }
    50% { transform: translate(-120px, -50px); }
    75% { transform: translate(70px, -70px); }
}

@keyframes float3 {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(120px, -80px); }
    50% { transform: translate(-60px, -120px); }
    75% { transform: translate(-100px, 80px); }
}

@keyframes sphereFloat {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(-30px, 30px); }
}

@keyframes pulseHeight {
    0%, 100% { height: 60px; opacity: 0.5; }
    50% { height: 80px; opacity: 0.8; }
}

@keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.3); }
}

@keyframes slideUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
    .nav-toggle {
        display: block;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
    }

    .hamburger {
        display: block;
        position: relative;
        width: 24px;
        height: 2px;
        background: var(--color-text);
        transition: all 0.3s ease;
    }

    .hamburger::before,
    .hamburger::after {
        content: '';
        position: absolute;
        width: 24px;
        height: 2px;
        background: var(--color-text);
        transition: all 0.3s ease;
    }

    .hamburger::before {
        top: -6px;
    }

    .hamburger::after {
        bottom: -6px;
    }

    .nav-list {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(10, 10, 10, 0.95);
        padding: 1rem 0;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .nav-list.active {
        display: flex;
    }

    .hero-content {
        padding: 1rem;
    }
}
