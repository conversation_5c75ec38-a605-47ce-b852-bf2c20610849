// ===== LUXURY PERFUME LANDING PAGE JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== HEADER SCROLL EFFECT =====
    const header = document.getElementById('header');
    let lastScrollTop = 0;
    
    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add scrolled class for background effect
        if (scrollTop > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        lastScrollTop = scrollTop;
    }
    
    // Throttled scroll event for better performance
    let ticking = false;
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(function() {
                handleScroll();
                ticking = false;
            });
            ticking = true;
        }
    });
    
    // ===== MOBILE NAVIGATION TOGGLE =====
    const mobileToggle = document.getElementById('mobile-toggle');
    const navMobile = document.getElementById('nav-mobile');
    const navMobileLinks = document.querySelectorAll('.nav-mobile-link');
    
    function toggleMobileNav() {
        mobileToggle.classList.toggle('active');
        navMobile.classList.toggle('active');
        
        // Prevent body scroll when mobile menu is open
        if (navMobile.classList.contains('active')) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
    }
    
    // Toggle mobile menu
    mobileToggle.addEventListener('click', toggleMobileNav);
    
    // Close mobile menu when clicking on a link
    navMobileLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navMobile.classList.contains('active')) {
                toggleMobileNav();
            }
        });
    });
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const isClickInsideNav = navMobile.contains(event.target);
        const isClickOnToggle = mobileToggle.contains(event.target);
        
        if (!isClickInsideNav && !isClickOnToggle && navMobile.classList.contains('active')) {
            toggleMobileNav();
        }
    });
    
    // ===== SMOOTH SCROLLING FOR NAVIGATION LINKS =====
    const allNavLinks = document.querySelectorAll('.nav-link, .nav-mobile-link');
    
    allNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Only handle internal links (starting with #)
            if (href && href.startsWith('#')) {
                e.preventDefault();
                
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const headerHeight = header.offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
    
    // ===== CTA BUTTON INTERACTION =====
    const ctaButton = document.getElementById('explore-btn');
    
    ctaButton.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            this.style.transform = '';
        }, 150);
        
        // Smooth scroll to collection section (if it exists)
        const collectionSection = document.getElementById('collection');
        if (collectionSection) {
            const headerHeight = header.offsetHeight;
            const targetPosition = collectionSection.offsetTop - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        } else {
            // If no collection section, show a message or redirect
            console.log('Explore Collection clicked - Add your collection section or redirect logic here');
        }
    });
    
    // ===== PARALLAX EFFECT FOR HERO BACKGROUND =====
    const heroBackground = document.querySelector('.hero-background');
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxSpeed = 0.5;
        
        if (heroBackground) {
            heroBackground.style.transform = `translateY(${scrolled * parallaxSpeed}px)`;
        }
    }
    
    // Throttled parallax effect
    let parallaxTicking = false;
    window.addEventListener('scroll', function() {
        if (!parallaxTicking) {
            requestAnimationFrame(function() {
                updateParallax();
                parallaxTicking = false;
            });
            parallaxTicking = true;
        }
    });
    
    // ===== INTERSECTION OBSERVER FOR ANIMATIONS =====
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, observerOptions);
    
    // Observe all fade-in elements
    const fadeElements = document.querySelectorAll('.fade-in-up');
    fadeElements.forEach(element => {
        element.style.animationPlayState = 'paused';
        observer.observe(element);
    });
    
    // ===== KEYBOARD NAVIGATION SUPPORT =====
    document.addEventListener('keydown', function(e) {
        // Close mobile menu with Escape key
        if (e.key === 'Escape' && navMobile.classList.contains('active')) {
            toggleMobileNav();
        }
        
        // Focus management for accessibility
        if (e.key === 'Tab') {
            // Add focus styles for keyboard navigation
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    // Remove keyboard navigation class on mouse use
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
    
    // ===== PERFORMANCE OPTIMIZATION =====
    // Preload critical images
    function preloadImages() {
        const imageUrls = [
            // Add any background images or critical images here
        ];
        
        imageUrls.forEach(url => {
            const img = new Image();
            img.src = url;
        });
    }
    
    // Call preload function
    preloadImages();
    
    // ===== RESIZE HANDLER =====
    let resizeTimeout;
    window.addEventListener('resize', function() {
        // Close mobile menu on resize to desktop
        if (window.innerWidth >= 768 && navMobile.classList.contains('active')) {
            toggleMobileNav();
        }
        
        // Debounce resize events
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Add any resize-specific logic here
            console.log('Window resized');
        }, 250);
    });
    
    // ===== INITIAL SETUP =====
    // Set initial scroll position
    handleScroll();
    
    // Add loaded class to body for CSS transitions
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 100);
    
    console.log('Luxe Noir landing page initialized successfully');
});

// ===== UTILITY FUNCTIONS =====

// Smooth scroll utility function
function smoothScrollTo(targetPosition, duration = 1000) {
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;
    
    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }
    
    // Easing function
    function ease(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }
    
    requestAnimationFrame(animation);
}

// Debounce utility function
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// Throttle utility function
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
