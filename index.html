<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxe Parfum | Exclusive Fragrances</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>

    <!-- Background animation elements -->
    <div class="bg-animation">
        <div class="shape shape-red"></div>
        <div class="shape shape-purple"></div>
        <div class="shape shape-blue"></div>
        <div class="grid-overlay"></div>

        <!-- Connection points for background effect -->
        <div class="connection-point" style="top: 25%; left: 30%"></div>
        <div class="connection-point" style="top: 40%; left: 48%"></div>
        <div class="connection-point" style="top: 35%; left: 70%"></div>
        <div class="connection-point" style="top: 60%; left: 20%"></div>
        <div class="connection-point" style="top: 65%; left: 65%"></div>
        <div class="connection-point" style="top: 30%; left: 15%"></div>
        <div class="connection-point" style="top: 55%; left: 80%"></div>

        <!-- Connection lines -->
        <div class="connection-line" style="top: 25%; left: 30%; width: 200px; transform: rotate(30deg);"></div>
        <div class="connection-line" style="top: 40%; left: 48%; width: 150px; transform: rotate(-20deg);"></div>
        <div class="connection-line" style="top: 60%; left: 20%; width: 250px; transform: rotate(10deg);"></div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="#" class="logo">LUXE PARFUM</a>
            <nav class="nav">
                <button class="nav-toggle" aria-label="Toggle navigation">
                    <span class="hamburger"></span>
                </button>
                <ul class="nav-list">
                    <li><a href="#" class="nav-link">Collections</a></li>
                    <li><a href="#" class="nav-link">About</a></li>
                    <li><a href="#" class="nav-link">Boutiques</a></li>
                    <li><a href="#" class="nav-link">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="accent-line"></div>
            <h1 class="hero-title">Discover Your Signature Scent</h1>
            <p class="hero-subtitle">Crafted with passion, worn with distinction</p>
            <button class="cta-button">
                <span class="button-text">Explore Collection</span>
                <span class="button-shine"></span>
            </button>
            <div class="floating-elements">
                <span class="floating-dot dot1"></span>
                <span class="floating-dot dot2"></span>
                <span class="floating-dot dot3"></span>
            </div>
        </div>
        <div class="hero-overlay"></div>
        <div class="gradient-sphere"></div>
    </section>

    <div class="mouse-cursor"></div>
    <div class="mouse-cursor-border"></div>

    <script src="js/main.js"></script>
</body>
</html>
