/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-dark: #0a0a0a;
    --secondary-dark: #1a1a1a;
    --accent-red: #dc143c;
    --accent-red-light: #ff1744;
    --accent-red-dark: #b71c1c;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    
    /* Typography */
    --font-serif: 'Playfair Display', serif;
    --font-sans: 'Inter', sans-serif;
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 2rem;
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    background-color: var(--primary-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* ===== HEADER STYLES ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(220, 20, 60, 0.1);
    transition: var(--transition-smooth);
}

.header.scrolled {
    background: rgba(10, 10, 10, 0.98);
    box-shadow: 0 4px 20px rgba(220, 20, 60, 0.1);
}

.nav-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo h1 {
    font-family: var(--font-serif);
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 2px;
}

.nav-desktop {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 400;
    position: relative;
    transition: var(--transition-smooth);
}

.nav-link:hover {
    color: var(--accent-red);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-red), var(--accent-red-light));
    transition: var(--transition-smooth);
}

.nav-link:hover::after {
    width: 100%;
}

/* Mobile Menu Toggle */
.mobile-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.hamburger-line {
    width: 25px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-smooth);
}

.mobile-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.nav-mobile {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(10, 10, 10, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(220, 20, 60, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);
}

.nav-mobile.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.nav-mobile-list {
    list-style: none;
    padding: 1rem 0;
}

.nav-mobile-link {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    padding: 1rem;
    transition: var(--transition-smooth);
    border-left: 3px solid transparent;
}

.nav-mobile-link:hover {
    color: var(--accent-red);
    background: rgba(220, 20, 60, 0.05);
    border-left-color: var(--accent-red);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        var(--primary-dark) 0%, 
        var(--secondary-dark) 50%, 
        rgba(220, 20, 60, 0.1) 100%);
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(220, 20, 60, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(220, 20, 60, 0.05) 0%, transparent 50%);
    animation: backgroundPulse 8s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(10, 10, 10, 0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: center;
    padding-top: 6rem;
}

.hero-text {
    text-align: center;
}

.hero-subtitle {
    font-family: var(--font-sans);
    font-size: 1rem;
    font-weight: 400;
    color: var(--accent-red);
    letter-spacing: 3px;
    text-transform: uppercase;
    margin-bottom: 1rem;
}

.hero-title {
    font-family: var(--font-serif);
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.accent-text {
    color: var(--accent-red);
    background: linear-gradient(45deg, var(--accent-red), var(--accent-red-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto 2rem;
    line-height: 1.7;
}

.hero-cta {
    margin-top: 2rem;
}

/* CTA Button with Glow Effect */
.cta-button {
    position: relative;
    background: linear-gradient(45deg, var(--accent-red), var(--accent-red-light));
    color: var(--text-primary);
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    border-radius: 50px;
    cursor: pointer;
    overflow: hidden;
    transition: var(--transition-smooth);
    box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 20, 60, 0.5);
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
    transition: var(--transition-smooth);
}

.cta-button:hover .button-glow {
    left: 100%;
}

/* Hero Image - Perfume Bottle */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.perfume-bottle {
    width: 200px;
    height: 300px;
    background: linear-gradient(135deg, 
        rgba(220, 20, 60, 0.1) 0%, 
        rgba(220, 20, 60, 0.05) 50%, 
        transparent 100%);
    border: 2px solid rgba(220, 20, 60, 0.3);
    border-radius: 20px 20px 40px 40px;
    position: relative;
    animation: float 6s ease-in-out infinite;
}

.perfume-bottle::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 30px;
    background: linear-gradient(45deg, var(--accent-red), var(--accent-red-light));
    border-radius: 10px 10px 0 0;
}

.bottle-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, 
        rgba(220, 20, 60, 0.2) 0%, 
        transparent 70%);
    border-radius: 30px 30px 50px 50px;
    animation: glow 4s ease-in-out infinite alternate;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: var(--accent-red);
    position: relative;
    animation: scrollBounce 2s infinite;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--accent-red);
    border-bottom: 2px solid var(--accent-red);
    transform: rotate(45deg);
}

@keyframes scrollBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Animation Classes */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease-out forwards;
}

.fade-in-up:nth-child(1) { animation-delay: 0.2s; }
.fade-in-up:nth-child(2) { animation-delay: 0.4s; }
.fade-in-up:nth-child(3) { animation-delay: 0.6s; }
.fade-in-up:nth-child(4) { animation-delay: 0.8s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (min-width: 768px) {
    .nav-desktop {
        display: block;
    }
    
    .mobile-toggle {
        display: none;
    }
    
    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        padding-top: 4rem;
    }
    
    .hero-text {
        text-align: left;
    }
    
    .hero-title {
        font-size: 3.5rem;
    }
    
    .hero-description {
        margin: 0 0 2rem 0;
    }
    
    .perfume-bottle {
        width: 250px;
        height: 350px;
    }
}

@media (min-width: 1024px) {
    .hero-title {
        font-size: 4rem;
    }
    
    .hero-description {
        font-size: 1.2rem;
    }
    
    .perfume-bottle {
        width: 300px;
        height: 400px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .cta-button {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }
    
    .perfume-bottle {
        width: 150px;
        height: 250px;
    }
}
